import imaplib
import email
from email.header import decode_header
from email import utils as email_utils
import sys
import requests # 需要 requests 库来获取 access_token
import os # 用于检查文件是否存在
import re # 用于HTML标签清理

# --- 配置文件名 ---
ACCOUNT_TOKEN_FILE = "outlook.txt"

# --- OAuth 和 IMAP 配置 ---
CLIENT_ID = '9e5f94bc-e8a4-4e73-b8be-63364c29d753'
IMAP_SERVER = 'outlook.live.com'
IMAP_PORT = 993
TOKEN_URL = 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token'

# --- 全局变量 ---
email_uids_cache = []
imap_conn = None
current_email_address = ""
current_refresh_token = ""
current_folder = "INBOX"  # 跟踪当前选择的文件夹

# --- 辅助函数 ---
def decode_header_value(header_value):
    if header_value is None: return ""
    decoded_string = ""
    try:
        parts = decode_header(str(header_value))
        for part, charset in parts:
            if isinstance(part, bytes):
                try: decoded_string += part.decode(charset if charset else 'utf-8', 'replace')
                except LookupError: decoded_string += part.decode('utf-8', 'replace')
            else: decoded_string += str(part)
    except Exception:
        if isinstance(header_value, str): return header_value
        try: return str(header_value, 'utf-8', 'replace') if isinstance(header_value, bytes) else str(header_value)
        except: return "[Header Decode Error]"
    return decoded_string

def fetch_access_token(refresh_token_val, client_id_val):
    print("正在获取新的 access_token...")
    try:
        # 使用系统环境变量的代理设置（http_proxy=http://127.0.0.1:7890）
        response = requests.post(TOKEN_URL, data={
            'client_id': client_id_val,
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token_val,
            'scope': 'https://outlook.office.com/IMAP.AccessAsUser.All offline_access'
        })  # 自动使用环境变量代理
        response.raise_for_status()
        token_data = response.json()
        access_token = token_data.get('access_token')
        if not access_token:
            print(f"获取 access_token 失败: {token_data.get('error_description', '响应中未找到 access_token')}")
            return None
        new_refresh_token = token_data.get('refresh_token')
        if new_refresh_token and new_refresh_token != refresh_token_val: # 仅当新的refresh_token与旧的不同时才更新并提示
            global current_refresh_token
            current_refresh_token = new_refresh_token
            print("提示: refresh_token 已被服务器更新。")
        print("成功获取 access_token。")
        return access_token
    except requests.exceptions.HTTPError as http_err:
        print(f"请求 access_token 时发生HTTP错误: {http_err}")
        if http_err.response is not None: print(f"服务器响应: {http_err.response.status_code} - {http_err.response.text}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求 access_token 时发生网络错误: {e}")
        return None
    except Exception as e:
        print(f"解析 access_token 响应时出错: {e}")
        return None

def connect_to_outlook(email_address_val, refresh_token_val, folder='INBOX'):
    global imap_conn, current_email_address, current_refresh_token, current_folder
    current_email_address = email_address_val
    current_folder = folder
    # 确保 current_refresh_token 使用传入的，因为 fetch_access_token 内部会更新全局的
    # 如果服务器返回了新的 refresh_token，则 current_refresh_token 会被 fetch_access_token 更新
    # 初始调用时，我们信任传入的 refresh_token_val
    current_refresh_token = refresh_token_val 

    access_token = fetch_access_token(current_refresh_token, CLIENT_ID) # 使用 current_refresh_token
    if not access_token: return False

    try:
        print(f"正在连接到 IMAP 服务器 {IMAP_SERVER}...")
        # 添加超时机制，避免卡住
        import socket
        socket.setdefaulttimeout(30)  # 设置30秒超时
        imap_conn = imaplib.IMAP4_SSL(IMAP_SERVER, IMAP_PORT)
        auth_string = f"user={email_address_val}\1auth=Bearer {access_token}\1\1"
        print("正在使用 XOAUTH2 进行认证...")
        typ, data = imap_conn.authenticate('XOAUTH2', lambda x: auth_string.encode('utf-8'))

        if typ == 'OK':
            print("IMAP XOAUTH2 认证成功。")
            print(f"正在选择 {folder}...")
            try:
                stat_select, data_select = imap_conn.select(folder, readonly=True)
                if stat_select == 'OK':
                    print(f"{folder} 已成功选择。")
                    return True
                else:
                    error_msg_select = data_select[0].decode('utf-8', 'replace') if data_select and data_select[0] else "未知错误"
                    print(f"选择 {folder} 失败: {error_msg_select}")
                    try: 
                        if imap_conn: imap_conn.close(); imap_conn.logout()
                    except: pass
                    imap_conn = None
                    return False
            except Exception as e_select:
                print(f"选择 {folder} 时发生错误: {e_select}")
                try:
                    if imap_conn: imap_conn.close(); imap_conn.logout()
                except: pass
                imap_conn = None
                return False
        else:
            error_message = data[0].decode('utf-8', 'replace') if data and data[0] else "未知认证错误"
            print(f"IMAP XOAUTH2 认证失败: {error_message} (Type: {typ})")
            try:
                if imap_conn: imap_conn.close()
            except: pass
            imap_conn = None
            return False
    except imaplib.IMAP4.error as e:
        print(f"IMAP 连接或认证时发生错误: {e}")
        if imap_conn: 
            try: 
                imap_conn.shutdown()
            except: 
                pass
        imap_conn = None
        return False
    except Exception as e:
        print(f"连接期间发生意外错误: {e}")
        if imap_conn: 
            try: 
                imap_conn.shutdown()
            except: 
                pass
        imap_conn = None
        return False

def fetch_all_email_uids():
    global email_uids_cache, imap_conn
    if not imap_conn:
        print("未连接到 IMAP 服务器 (fetch_all_email_uids)。")
        return False
    try:
        typ, uid_data = imap_conn.uid('search', None, "ALL")
        if typ != 'OK':
            print(f"搜索邮件失败 (status: {typ}). 可能需要重新认证。")
            return False
        if not uid_data[0]:
            email_uids_cache = []
            print("INBOX 中没有邮件。")
            return True
        uids = uid_data[0].split()
        email_uids_cache = [uid for uid in uids if uid]
        email_uids_cache.reverse()
        print(f"找到 {len(email_uids_cache)} 封邮件。")
        return True
    except (imaplib.IMAP4.error, OSError) as e:
        print(f"获取邮件 UIDs 时发生错误 (可能连接已断开): {e}")
        close_connection()
        return False
    except Exception as e:
        print(f"获取邮件 UIDs 时发生未知错误: {e}")
        email_uids_cache = []
        return False

def list_emails_paginated_fast(page_number, emails_per_page=10):
    """快速分页获取邮件，不需要预加载所有UID"""
    global imap_conn
    if not imap_conn:
        print("Not connected (list_emails_paginated_fast). Please try refreshing (r) or restart the program.")
        return None
    
    try:
        # 直接从IMAP获取所有UID，但只处理需要的部分
        typ, uid_data = imap_conn.uid('search', None, "ALL")
        if typ != 'OK':
            print(f"搜索邮件失败 (status: {typ}). 可能需要重新认证。")
            return None
        
        if not uid_data[0]:
            print("当前文件夹中没有邮件。")
            return []
        
        all_uids = uid_data[0].split()
        all_uids.reverse()  # 最新邮件在前
        
        # 计算分页范围
        start_index = page_number * emails_per_page
        end_index = start_index + emails_per_page
        page_uids = all_uids[start_index:end_index]
        
        if not page_uids:
            return []
        
        displayed_emails_info = []
        for i, uid_bytes in enumerate(page_uids):
            list_display_number = start_index + i + 1
            subject_str = "(No Subject)"
            formatted_date_str = "(No Date)"
            
            try:
                typ, msg_data = imap_conn.uid('fetch', uid_bytes, '(BODY.PEEK[HEADER.FIELDS (SUBJECT DATE)])')
                if typ == 'OK' and msg_data and msg_data[0] is not None:
                    header_content_bytes = None
                    if isinstance(msg_data[0], tuple) and len(msg_data[0]) == 2 and isinstance(msg_data[0][1], bytes):
                        header_content_bytes = msg_data[0][1]
                    elif isinstance(msg_data, list) and len(msg_data) > 1 and \
                         isinstance(msg_data[0], bytes) and isinstance(msg_data[1], bytes) and \
                         (b"FIELDS" in msg_data[0].upper() or b"SUBJECT" in msg_data[0].upper()):
                         header_content_bytes = msg_data[1]
                    
                    if header_content_bytes:
                        import email
                        from email.header import decode_header
                        header_message = email.message_from_bytes(header_content_bytes)
                        
                        # 解码主题
                        raw_subject = header_message.get('Subject')
                        if raw_subject:
                            decoded_subject = ""
                            try:
                                parts = decode_header(raw_subject)
                                for part, charset in parts:
                                    if isinstance(part, bytes):
                                        try:
                                            decoded_subject += part.decode(charset if charset else 'utf-8', 'replace')
                                        except LookupError:
                                            decoded_subject += part.decode('utf-8', 'replace')
                                    else:
                                        decoded_subject += str(part)
                                subject_str = decoded_subject
                            except:
                                subject_str = str(raw_subject)
                        
                        # 解码日期
                        raw_date = header_message.get('Date')
                        if raw_date:
                            try:
                                from email.utils import parsedate_to_datetime
                                date_obj = parsedate_to_datetime(raw_date)
                                formatted_date_str = date_obj.strftime('%Y-%m-%d %H:%M')
                            except:
                                formatted_date_str = str(raw_date)[:19]
                
            except Exception as e:
                print(f"获取邮件 {uid_bytes} 头部信息时出错: {e}")
            
            displayed_emails_info.append({
                'list_index': list_display_number,
                'uid': uid_bytes,
                'subject': subject_str,
                'date': formatted_date_str
            })
        
        return displayed_emails_info
        
    except Exception as e:
        print(f"快速分页获取邮件时发生错误: {e}")
        return None

def list_emails_paginated(page_number, emails_per_page=10):
    global email_uids_cache, imap_conn
    if not imap_conn:
        print("Not connected (list_emails_paginated). Please try refreshing (r) or restart the program.")
        return None
    if not email_uids_cache:
        print("Email cache is empty, or failed to fetch email list. Please try refreshing (r).")
        return None
    start_index = page_number * emails_per_page
    end_index = start_index + emails_per_page
    page_uids = email_uids_cache[start_index:end_index]
    if not page_uids:
        print("No more emails on this page.")
        return []
    print(f"\n--- Email List (Page {page_number + 1}) ---")
    displayed_emails_info = []
    for i, uid_bytes in enumerate(page_uids):
        list_display_number = start_index + i + 1
        subject_str = "(No Subject)"
        formatted_date_str = "(No Date)"
        try:
            typ, msg_data = imap_conn.uid('fetch', uid_bytes, '(BODY.PEEK[HEADER.FIELDS (SUBJECT DATE)])')
            if typ == 'OK' and msg_data and msg_data[0] is not None:
                header_content_bytes = None
                if isinstance(msg_data[0], tuple) and len(msg_data[0]) == 2 and isinstance(msg_data[0][1], bytes):
                    header_content_bytes = msg_data[0][1]
                elif isinstance(msg_data, list) and len(msg_data) > 1 and \
                     isinstance(msg_data[0], bytes) and isinstance(msg_data[1], bytes) and \
                     (b"FIELDS" in msg_data[0].upper() or b"SUBJECT" in msg_data[0].upper()):
                     header_content_bytes = msg_data[1]
                if header_content_bytes:
                    header_message = email.message_from_bytes(header_content_bytes)
                    subject_str = decode_header_value(header_message.get('Subject', '(No Subject)'))
                    date_header_str = header_message.get('Date')
                    if date_header_str:
                        try:
                            dt_obj = email_utils.parsedate_to_datetime(date_header_str)
                            if dt_obj: formatted_date_str = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
                            else: formatted_date_str = "(Invalid Date Format)"
                        except Exception: formatted_date_str = f"({date_header_str[:25]}...)"
                else: subject_str = "(Failed to parse header structure from response)"
            elif typ != 'OK':
                 error_detail = (msg_data[0].decode('utf-8', 'replace') if msg_data and msg_data[0] else "Unknown error")
                 subject_str = f"(Failed to fetch header: {typ} {error_detail})"
            print(f"{list_display_number}. Subject: {subject_str} - {formatted_date_str}")
            displayed_emails_info.append({'list_index': list_display_number, 'uid': uid_bytes, 'subject': subject_str, 'date': formatted_date_str})
        except (imaplib.IMAP4.error, OSError) as e:
            print(f"{list_display_number}. Subject: (Connection error while fetching: {e}) - Date: ")
            displayed_emails_info.append({'list_index': list_display_number, 'uid': uid_bytes, 'subject': "Connection Error", 'date': ""})
            close_connection(); return None 
        except Exception as e:
            print(f"{list_display_number}. Subject: (Error parsing list item: {e}) - Date: ")
            displayed_emails_info.append({'list_index': list_display_number, 'uid': uid_bytes, 'subject': "Parsing Error", 'date': ""})
    return displayed_emails_info

def clean_html_content(html_content):
    """简单的HTML标签清理功能，提取可读文本"""
    if not html_content:
        return ""
    
    # 移除HTML标签
    clean_text = re.sub(r'<[^>]+>', '', html_content)
    
    # 解码HTML实体
    import html
    clean_text = html.unescape(clean_text)
    
    # 清理多余的空白字符
    clean_text = re.sub(r'\s+', ' ', clean_text)
    clean_text = re.sub(r'\n\s*\n', '\n\n', clean_text)
    
    return clean_text.strip()

def view_email_details(selected_uid_bytes):
    global imap_conn
    if not imap_conn: print("Not connected (view_email_details)."); return
    if not selected_uid_bytes: print("Invalid email UID."); return
    current_uid_str = selected_uid_bytes.decode('utf-8','replace') if isinstance(selected_uid_bytes, bytes) else str(selected_uid_bytes)
    print(f"DEBUG: Attempting to view UID: {current_uid_str}")
    try:
        fetch_item = '(RFC822)'
        print(f"DEBUG: Fetching UID {current_uid_str} with {fetch_item}...")
        typ, msg_data = imap_conn.uid('fetch', selected_uid_bytes, fetch_item)
        if typ == 'OK' and msg_data and msg_data[0] is not None:
            raw_email_bytes = None
            if isinstance(msg_data[0], tuple) and len(msg_data[0]) == 2 and isinstance(msg_data[0][1], bytes):
                raw_email_bytes = msg_data[0][1]
            elif isinstance(msg_data, list):
                for item_part in msg_data:
                    if isinstance(item_part, tuple) and len(item_part) == 2 and isinstance(item_part[1], bytes):
                        raw_email_bytes = item_part[1]; break
            if raw_email_bytes is None:
                print(f"Fetched UID {current_uid_str} {fetch_item} successfully, but failed to extract email data from response: {msg_data}"); return
            email_message = email.message_from_bytes(raw_email_bytes)
            print("\n--- Email Details ---")
            subject = decode_header_value(email_message['Subject']) or "(No Subject)"
            from_ = decode_header_value(email_message['From']) or "(Unknown Sender)"
            to_ = decode_header_value(email_message['To']) or "(Unknown Recipient)"
            date_ = email_message['Date'] or "(Unknown Date)"
            print(f"Subject: {subject}")
            print(f"From: {from_}")
            print(f"To: {to_}")
            print(f"Date: {date_}")
            print("\n--- Email Body ---")
            
            body_content = ""
            html_content = ""
            
            if email_message.is_multipart():
                # 首先尝试获取纯文本内容
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    if content_type == 'text/plain' and 'attachment' not in content_disposition.lower():
                        try:
                            charset = part.get_content_charset() or 'utf-8'
                            payload = part.get_payload(decode=True)
                            body_content = payload.decode(charset, errors='replace')
                            break 
                        except Exception: 
                            body_content = "[Failed to decode text part]"
                            break
                
                # 如果没有纯文本内容，获取HTML内容
                if not body_content:
                    for part in email_message.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))
                        if content_type == 'text/html' and 'attachment' not in content_disposition.lower():
                            try:
                                charset = part.get_content_charset() or 'utf-8'
                                payload = part.get_payload(decode=True)
                                html_content = payload.decode(charset, errors='replace')
                                break
                            except Exception: 
                                html_content = "[Failed to decode HTML part]"
                                break
            else:
                # 单部分邮件
                try:
                    charset = email_message.get_content_charset() or 'utf-8'
                    payload = email_message.get_payload(decode=True)
                    content_type = email_message.get_content_type()
                    content = payload.decode(charset, errors='replace')
                    
                    if content_type == 'text/html':
                        html_content = content
                    else:
                        body_content = content
                except Exception: 
                    body_content = "[Failed to decode email body]"
            
            # 显示邮件内容
            if body_content:
                print(f"邮件内容 (纯文本, {len(body_content)} 字符):")
                print(body_content)
            elif html_content:
                print(f"邮件内容 (HTML格式, {len(html_content)} 字符):")
                print("--- 原始HTML内容 ---")
                print(html_content)
                print("\n--- 提取的文本内容 ---")
                clean_text = clean_html_content(html_content)
                if clean_text:
                    print(clean_text)
                else:
                    print("[无法从HTML中提取可读文本]")
            else:
                print("[No readable text content found]")
                
        else:
            error_detail = msg_data[0].decode('utf-8', 'replace') if msg_data and msg_data[0] else "Unknown detail"
            print(f"Failed to fetch email (UID: {current_uid_str}) with {fetch_item}: Status={typ}, Detail={error_detail}")
    except (imaplib.IMAP4.error, OSError) as e:
        print(f"IMAP/Network error while viewing email details (UID: {current_uid_str}): {e}")
        close_connection()
    except Exception as e:
        print(f"Unknown error while viewing email details (UID: {current_uid_str}): {e}")

def close_connection():
    global imap_conn
    if imap_conn:
        try:
            print("正在关闭 IMAP 连接...")
            current_state = None
            if hasattr(imap_conn, 'state'): current_state = imap_conn.state
            if current_state == 'SELECTED':
                 print(f"DEBUG: 当前邮箱状态 '{current_state}', 尝试 close()...")
                 imap_conn.close()
                 if hasattr(imap_conn, 'state'): current_state = imap_conn.state 
            if current_state == 'AUTH' or \
               (current_state != 'LOGOUT' and hasattr(imap_conn, 'sock') and imap_conn.sock is not None):
                 print(f"DEBUG: 当前邮箱状态 '{current_state}', 尝试 logout()...")
                 imap_conn.logout()
                 print("IMAP 连接已成功注销并关闭。")
            elif current_state == 'LOGOUT': print("IMAP 连接已经处于注销状态。")
            else: print(f"DEBUG: 当前邮箱状态 '{current_state}'，可能无需或无法 logout。")
        except (imaplib.IMAP4.error, OSError, AttributeError) as e:
            print(f"关闭连接时发生错误: {type(e).__name__} - {e}")
            print("(这通常意味着连接在尝试关闭前已经失效或服务器未正常响应)")
        finally:
            imap_conn = None
            print("程序已清理连接对象。")

def ensure_connection(force_reconnect=False):
    global imap_conn, current_email_address, current_refresh_token, current_folder
    if not force_reconnect and imap_conn:
        try:
            status, _ = imap_conn.noop()
            if status == 'OK': return True
            else: 
                print(f"IMAP NOOP 状态非 OK ({status})，将尝试重连...")
                close_connection()
        except (imaplib.IMAP4.abort, imaplib.IMAP4.error, BrokenPipeError, OSError, AttributeError):
            print("IMAP 连接已断开 (检测到错误)。将尝试重新连接...")
            imap_conn = None 
    if force_reconnect and imap_conn:
        print("强制重新连接，关闭当前会话...")
        close_connection()
    if not current_email_address or not current_refresh_token:
        print("无可用凭据进行重新连接 (邮箱或refresh_token为空)。")
        return False
    print("尝试建立新连接/重新连接...")
    return connect_to_outlook(current_email_address, current_refresh_token, current_folder)

def list_available_folders():
    """列出所有可用的邮箱文件夹"""
    global imap_conn
    if not imap_conn:
        print("未连接到 IMAP 服务器。")
        return []
    try:
        typ, folders = imap_conn.list()
        if typ != 'OK':
            print(f"获取文件夹列表失败: {typ}")
            return []
        
        folder_list = []
        print("\n--- 可用文件夹 ---")
        print("DEBUG: 原始IMAP响应:")
        
        for folder_info in folders:
            folder_str = folder_info.decode('utf-8') if isinstance(folder_info, bytes) else str(folder_info)
            print(f"DEBUG: {folder_str}")
            
            # 使用简化的解析逻辑
            folder_name = None
            parts = folder_str.split()
            if len(parts) >= 3:
                folder_name = parts[-1]  # 取最后一部分作为文件夹名
            
            if folder_name and folder_name != '/':
                folder_list.append(folder_name)
                print(f"  - {folder_name}")
        
        return folder_list
    except Exception as e:
        print(f"获取文件夹列表时发生错误: {e}")
        return []

def switch_to_folder(folder_name):
    """切换到指定文件夹"""
    global imap_conn, current_folder, email_uids_cache
    if not imap_conn:
        print("未连接到 IMAP 服务器。")
        return False
    
    try:
        print(f"正在切换到文件夹: {folder_name}")
        # 先关闭当前文件夹
        if hasattr(imap_conn, 'state') and imap_conn.state == 'SELECTED':
            imap_conn.close()
        
        # 选择新文件夹
        stat_select, data_select = imap_conn.select(folder_name, readonly=True)
        if stat_select == 'OK':
            current_folder = folder_name
            email_uids_cache = []  # 清空邮件缓存
            print(f"已成功切换到 {folder_name}")
            return True
        else:
            error_msg = data_select[0].decode('utf-8', 'replace') if data_select and data_select[0] else "未知错误"
            print(f"切换到 {folder_name} 失败: {error_msg}")
            # 尝试重新选择原文件夹
            try:
                imap_conn.select(current_folder, readonly=True)
            except:
                pass
            return False
    except Exception as e:
        print(f"切换文件夹时发生错误: {e}")
        return False

# --- 新增账号文件处理函数 ---
def load_accounts_from_file(filename=ACCOUNT_TOKEN_FILE):
    """从文件加载账号信息"""
    accounts = []
    if not os.path.exists(filename):
        return accounts
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and "---" in line:
                    parts = line.split("---", 1)
                    if len(parts) == 2:
                        accounts.append({'email': parts[0].strip(), 'token': parts[1].strip()})
                    else:
                        print(f"警告: 文件 '{filename}' 中发现格式错误的行: {line}")
    except IOError as e:
        print(f"读取账号文件 '{filename}' 时出错: {e}")
    return accounts

def save_account_to_file(filename, email_to_save, token_to_save):
    """保存或更新单个账号信息到文件，确保唯一性并保留顺序（如果可能）。"""
    accounts = load_accounts_from_file(filename) # 先读取现有账号
    
    email_exists = False
    updated_accounts = []
    new_account_added_or_updated = False

    for acc in accounts:
        if acc['email'] == email_to_save:
            if acc['token'] != token_to_save:
                print(f"更新账号 {email_to_save} 的 refresh_token。")
                updated_accounts.append({'email': email_to_save, 'token': token_to_save})
                new_account_added_or_updated = True
            else:
                # Token 未变，保留原样
                updated_accounts.append(acc)
                print(f"账号 {email_to_save} 的信息未改变，无需更新。")
            email_exists = True
        else:
            updated_accounts.append(acc)
            
    if not email_exists:
        print(f"添加新账号 {email_to_save} 到列表。")
        updated_accounts.append({'email': email_to_save, 'token': token_to_save})
        new_account_added_or_updated = True

    if new_account_added_or_updated or not email_exists : # 只有当列表实际改变或新账号添加时才重写文件
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                for acc in updated_accounts:
                    f.write(f"{acc['email']}---{acc['token']}\n")
            print(f"账号信息已保存到 {filename}")
        except IOError as e:
            print(f"保存账号信息到文件 {filename} 时出错: {e}")
    # else:
        # print("DEBUG: No change in account list, file not rewritten.")

def import_accounts_from_batch_file(batch_filename):
    """从批量导入文件中导入账号信息"""
    print(f"正在从 {batch_filename} 导入账号...")
    
    if not os.path.exists(batch_filename):
        print(f"错误: 文件 '{batch_filename}' 不存在。")
        return
    
    success_count = 0
    skip_count = 0
    error_count = 0
    
    try:
        with open(batch_filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                
                # 解析格式: 邮箱----x----client_id----token
                if line.count('----') >= 3:
                    parts = line.split('----')
                    if len(parts) >= 4:
                        email = parts[0].strip()
                        # parts[1] 是密码占位符，忽略
                        client_id = parts[2].strip()
                        token = '----'.join(parts[3:]).strip()  # token可能包含----符号
                        
                        # 验证邮箱格式
                        if '@' not in email:
                            print(f"第 {line_num} 行: 邮箱格式无效 '{email}'")
                            error_count += 1
                            continue
                            
                        # 验证Client ID（如果需要匹配）
                        if client_id != CLIENT_ID:
                            print(f"第 {line_num} 行: Client ID 不匹配，跳过 {email}")
                            skip_count += 1
                            continue
                            
                        # 验证token不为空
                        if not token:
                            print(f"第 {line_num} 行: Token 为空，跳过 {email}")
                            error_count += 1
                            continue
                            
                        # 保存账号
                        try:
                            save_account_to_file(ACCOUNT_TOKEN_FILE, email, token)
                            success_count += 1
                        except Exception as e:
                            print(f"第 {line_num} 行: 保存账号 {email} 时出错: {e}")
                            error_count += 1
                    else:
                        print(f"第 {line_num} 行: 格式错误，部分数量不足")
                        error_count += 1
                else:
                    print(f"第 {line_num} 行: 格式错误，分隔符不足")
                    error_count += 1
                    
    except IOError as e:
        print(f"读取批量导入文件时出错: {e}")
        return
    except Exception as e:
        print(f"处理批量导入文件时发生未知错误: {e}")
        return
    
    # 输出导入结果统计
    print(f"\n=== 批量导入完成 ===")
    print(f"成功导入: {success_count} 个账号")
    print(f"跳过 (Client ID不匹配): {skip_count} 个账号")
    print(f"错误: {error_count} 个账号")
    print(f"总计处理: {success_count + skip_count + error_count} 个账号")
    
    if success_count > 0:
        print(f"账号已保存到 {ACCOUNT_TOKEN_FILE}")

# --- 主程序逻辑 ---
def main():
    global email_uids_cache, imap_conn, current_email_address, current_refresh_token

    print("Outlook 邮件控制台客户端 (OAuth XOAUTH2)")
    print("=======================================")
    print(f"(将使用IMAP服务器: {IMAP_SERVER}, Client ID: {CLIENT_ID[:10]}...)")
    
    # --- 账号选择/输入逻辑 ---
    loaded_accounts = load_accounts_from_file(ACCOUNT_TOKEN_FILE)
    user_input_email = None
    user_input_refresh_token = None # 这是用户最初提供或选择的token
    
    if loaded_accounts:
        print("\n已保存的账号:")
        for i, acc in enumerate(loaded_accounts):
            print(f"  {i+1}. {acc['email']}")
        print(f"  {len(loaded_accounts)+1}. 输入新的邮箱和 refresh_token")
        print(f"  {len(loaded_accounts)+2}. 批量导入账号")
        
        while True:
            choice = input(f"请选择账号 (1-{len(loaded_accounts)+2}) 或直接输入新邮箱: ").strip()
            if choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(loaded_accounts):
                    selected_account = loaded_accounts[choice_num-1]
                    user_input_email = selected_account['email']
                    user_input_refresh_token = selected_account['token'] # 使用文件中存储的token
                    print(f"已选择账号: {user_input_email}")
                    break
                elif choice_num == len(loaded_accounts) + 1:
                    user_input_email = input("请输入新的 Outlook 邮箱地址: ").strip()
                    user_input_refresh_token = input("请输入新的 refresh_token: ").strip()
                    break
                elif choice_num == len(loaded_accounts) + 2:
                    # 批量导入功能
                    import_filename = input("请输入要导入的文件名（例如 info.txt）: ").strip()
                    if import_filename:
                        import_accounts_from_batch_file(import_filename)
                        # 重新加载账号列表
                        loaded_accounts = load_accounts_from_file(ACCOUNT_TOKEN_FILE)
                        if loaded_accounts:
                            print("\n导入完成，请重新选择账号:")
                            for i, acc in enumerate(loaded_accounts):
                                print(f"  {i+1}. {acc['email']}")
                            print(f"  {len(loaded_accounts)+1}. 输入新的邮箱和 refresh_token")
                            print(f"  {len(loaded_accounts)+2}. 批量导入账号")
                        else:
                            print("导入完成，但没有有效账号。请手动输入账号信息。")
                    else:
                        print("未输入文件名，取消导入。")
                    continue  # 继续选择流程
                else:
                    print("无效选择，请输入列表中的数字或直接输入新邮箱。")
            elif "@" in choice and choice.count("---") == 0 : # 用户可能直接输入了邮箱
                user_input_email = choice
                user_input_refresh_token = input(f"请输入邮箱 {user_input_email} 的 refresh_token: ").strip()
                break
            elif "---" in choice : # 用户可能直接输入了 邮箱---token
                 parts = choice.split("---",1)
                 if len(parts) == 2 and "@" in parts[0].strip() and parts[1].strip():
                     user_input_email = parts[0].strip()
                     user_input_refresh_token = parts[1].strip()
                     print(f"已识别邮箱: {user_input_email}")
                     break
                 else:
                     print("输入格式错误，请选择数字，或输入邮箱，或输入 '邮箱---token'")
            else:
                print("无效输入，请选择列表中的数字，或直接输入邮箱。")

    else: # 没有已保存的账号
        print("\n未找到已保存的账号信息。")
        user_input_email = input("请输入 Outlook 邮箱地址: ").strip()
        user_input_refresh_token = input("请输入 refresh_token: ").strip()

    # --- 连接和后续操作 ---
    # current_refresh_token 将在 connect_to_outlook 中被设置为 user_input_refresh_token，
    # 然后可能在 fetch_access_token 中被服务器返回的新 token 更新。
    if not connect_to_outlook(user_input_email, user_input_refresh_token):
        print("无法连接到邮箱。请检查您的邮箱地址、refresh_token、网络连接以及 Client ID 是否正确。")
        input("按 Enter 键退出..."); return 
    
    # 连接成功后，使用 current_email_address 和 可能已更新的 current_refresh_token 保存账号
    save_account_to_file(ACCOUNT_TOKEN_FILE, current_email_address, current_refresh_token)

    current_page = 0
    emails_per_page = 10
    displayed_emails_info_on_page = [] 

    if fetch_all_email_uids():
        displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
        if displayed_emails_info_on_page is None and imap_conn is None:
            print("在列出邮件时连接中断。请尝试重启程序。"); input("按 Enter 键退出..."); return
    else:
        if not imap_conn:
             print("首次获取邮件列表失败，由于连接问题。请尝试重启程序。"); input("按 Enter 键退出..."); return
        print("首次获取邮件列表失败 (邮箱可能为空或发生错误)。")
        
    while True:
        print(f"\n当前文件夹: {current_folder}")
        print("\n操作选项:")
        print("  l - 再次列出当前页邮件")
        print("  n - 下一页")
        print("  p - 上一页")
        # print("  v - 查看邮件详情 (旧方式，两步输入)") # 可以移除旧的v提示
        print("  v-{邮件编号} - 直接查看邮件 (例如 v-1)")
        print("  r - 刷新邮箱")
        print("  i - 切换到收件箱")
        print("  j - 切换到垃圾箱")
        print("  f - 列出所有文件夹")
        print("  f-{文件夹名} - 切换到指定文件夹")
        print("  q - 退出")
        action_raw = input("请输入操作: ").strip() # 保留原始输入以便解析 v-{id}
        action = action_raw.lower()

        if action not in ['q']:
            if not ensure_connection():
                print("\n无法建立或恢复 IMAP 连接。请尝试 'q' 退出并检查您的网络和凭据。"); continue 
        
        # --- 新的 v-{邮件编号} 命令解析 ---
        if action.startswith("v-"):
            parts = action_raw.split('-', 1)
            if len(parts) == 2 and parts[1].isdigit():
                choice_num_str = parts[1]
                if not displayed_emails_info_on_page: 
                    print("当前页面没有邮件可供选择或列表为空。请先列出邮件 (l) 或刷新 (r)。"); continue
                
                selected_email_detail = None
                for email_detail in displayed_emails_info_on_page:
                    if str(email_detail['list_index']) == choice_num_str:
                        selected_email_detail = email_detail; break
                
                if selected_email_detail:
                    view_email_details(selected_email_detail['uid'])
                    if imap_conn is None:
                        print("在查看邮件时连接中断。请尝试刷新 'r' 或重启。"); continue
                else:
                    print(f"无效的邮件编号 '{choice_num_str}'。请输入列表中显示的编号。")
                continue # 命令已处理
            else:
                print("查看邮件命令格式错误。请使用 'v-{邮件编号}'，例如 'v-1'。")
                continue # 命令格式错误，已处理
        # --- 其他命令 ---
        elif action == 'l':
            if not email_uids_cache or not displayed_emails_info_on_page:
                print("邮件缓存为空或当前页无内容，尝试重新获取邮件列表...")
                if not fetch_all_email_uids(): print("刷新邮件列表失败。")
            displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
            if displayed_emails_info_on_page is None and imap_conn is None:
                print("在列出邮件时连接中断。请尝试刷新 'r' 或重启。"); continue
        elif action == 'n':
            if not email_uids_cache:
                print("无邮件加载。请尝试刷新 (r)。")
                displayed_emails_info_on_page = []; continue
            if (current_page + 1) * emails_per_page < len(email_uids_cache): current_page += 1
            else: print("已经是最后一页了。")
            displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
            if displayed_emails_info_on_page is None and imap_conn is None:
                 print("在列出邮件时连接中断。请尝试刷新 'r' 或重启。"); continue
        elif action == 'p':
            if current_page > 0: current_page -= 1
            else: print("已经是第一页了。")
            displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
            if displayed_emails_info_on_page is None and imap_conn is None:
                print("在列出邮件时连接中断。请尝试刷新 'r' 或重启。"); continue
        # elif action == 'v': # 旧的 v 命令逻辑，可以移除或注释掉
            # if not displayed_emails_info_on_page: 
            #     print("当前页面没有邮件可供选择或列表为空。请先列出邮件 (l) 或刷新 (r)。"); continue
            # try:
            #     choice_str = input(f"请输入要查看的邮件的列表编号: ").strip()
            #     # ... (旧的查找和调用 view_email_details 逻辑) ...
            # except ValueError: print("请输入有效的数字。")
            # except Exception as e: print(f"查看邮件时发生错误: {e}")
        elif action == 'r':
            print("正在刷新邮箱...")
            email_uids_cache = [] 
            current_page = 0
            if fetch_all_email_uids():
                displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
                if displayed_emails_info_on_page is None and imap_conn is None:
                     print("在刷新并列出邮件时连接中断。请尝试重启。"); continue
            else:
                if not imap_conn: print("刷新失败，因为 IMAP 连接已断开。请尝试重启程序或检查网络。")
                else: print("刷新失败，未能获取邮件列表 (邮箱可能为空或发生错误)。")
        elif action == 'i':
            if switch_to_folder("Inbox"):
                current_page = 0
                if fetch_all_email_uids():
                    displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
                    if displayed_emails_info_on_page is None and imap_conn is None:
                        print("在切换到收件箱后列出邮件时连接中断。"); continue
                else:
                    displayed_emails_info_on_page = []
            else:
                print("无法切换到 Inbox 文件夹，尝试使用 'f-Inbox' 命令。")
        elif action == 'j':
            # 尝试多个可能的垃圾箱文件夹名称
            possible_junk_folders = ["Junk Email", "Junk", "Spam", "垃圾邮件", "Deleted Items"]
            junk_folder_found = None
            
            # 从调试输出我们知道垃圾箱文件夹是 "Junk"，先直接尝试
            if switch_to_folder("Junk"):
                current_page = 0
                if fetch_all_email_uids():
                    displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
                    if displayed_emails_info_on_page is None and imap_conn is None:
                        print("在切换到垃圾箱后列出邮件时连接中断。"); continue
                else:
                    displayed_emails_info_on_page = []
            else:
                print("无法切换到 Junk 文件夹，尝试使用 'f-Junk' 命令。")
        elif action == 'f':
            list_available_folders()
        elif action.startswith('f-'):
            parts = action_raw.split('-', 1)
            if len(parts) == 2:
                folder_name = parts[1]
                if switch_to_folder(folder_name):
                    current_page = 0
                    if fetch_all_email_uids():
                        displayed_emails_info_on_page = list_emails_paginated(current_page, emails_per_page)
                        if displayed_emails_info_on_page is None and imap_conn is None:
                            print(f"在切换到 {folder_name} 后列出邮件时连接中断。"); continue
                    else:
                        displayed_emails_info_on_page = []
                else:
                    print(f"无法切换到 {folder_name}。请检查文件夹名称是否正确。")
            else:
                print("切换文件夹命令格式错误。请使用 'f-{文件夹名}'，例如 'f-Junk Email'。")
        elif action == 'q': break
        else: print("无效的操作，请重新输入。")
    close_connection()
    print("程序已退出。")

if __name__ == "__main__":
    main()