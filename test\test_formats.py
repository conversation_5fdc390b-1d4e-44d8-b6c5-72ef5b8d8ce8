#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两种格式的导入功能
"""

import requests
import json
import time

# 测试配置
API_BASE = "http://localhost:8000"
ADMIN_PASSWORD = "admin123"

def test_simple_format():
    """测试简单格式导入"""
    print("测试简单格式导入...")
    
    # 简单格式数据
    simple_data = """<EMAIL>---fake_token_1
<EMAIL>---fake_token_2
<EMAIL>---fake_token_3"""
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": simple_data}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"[OK] 简单格式导入成功，处理了 {len(results)} 个账户")
            
            for result in results:
                status = "[OK]" if result['status'] == 'success' else "[EXPECTED_FAIL]"
                print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
            
            return True
        else:
            print(f"[FAIL] 简单格式导入失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 简单格式测试失败: {e}")
        return False

def test_batch_format():
    """测试批量格式导入"""
    print("测试批量格式导入...")
    
    # 批量格式数据
    batch_data = """<EMAIL>----fake_pass----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_token_4
<EMAIL>----fake_pass----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_token_5
<EMAIL>----fake_pass----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_token_6"""
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": batch_data}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"[OK] 批量格式导入成功，处理了 {len(results)} 个账户")
            
            for result in results:
                status = "[OK]" if result['status'] == 'success' else "[EXPECTED_FAIL]"
                print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
            
            return True
        else:
            print(f"[FAIL] 批量格式导入失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 批量格式测试失败: {e}")
        return False

def test_mixed_format():
    """测试混合格式导入"""
    print("测试混合格式导入...")
    
    # 混合格式数据
    mixed_data = """<EMAIL>---fake_token_7
<EMAIL>----fake_pass----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_token_8
<EMAIL>---fake_token_9"""
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": mixed_data}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"[OK] 混合格式导入成功，处理了 {len(results)} 个账户")
            
            for result in results:
                status = "[OK]" if result['status'] == 'success' else "[EXPECTED_FAIL]"
                print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
            
            return True
        else:
            print(f"[FAIL] 混合格式导入失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 混合格式测试失败: {e}")
        return False

def main():
    """主函数"""
    print("测试两种格式的导入功能")
    print("="*50)
    
    # 等待服务器
    print("等待服务器启动...")
    for i in range(10):
        try:
            response = requests.get(f"{API_BASE}/docs", timeout=2)
            if response.status_code == 200:
                print("[OK] 服务器已启动")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("[FAIL] 服务器启动超时")
        return False
    
    # 运行测试
    tests = [
        ("简单格式", test_simple_format),
        ("批量格式", test_batch_format),
        ("混合格式", test_mixed_format),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'-'*30}")
        print(f"运行测试: {test_name}")
        print(f"{'-'*30}")
        
        if test_func():
            print(f"[OK] {test_name} 测试通过")
            passed += 1
        else:
            print(f"[FAIL] {test_name} 测试失败")
            failed += 1
    
    # 输出结果
    print(f"\n{'='*50}")
    print("测试结果总结")
    print(f"{'='*50}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    
    if failed == 0:
        print("\n[OK] 所有格式测试通过！")
        return True
    else:
        print(f"\n[FAIL] {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
