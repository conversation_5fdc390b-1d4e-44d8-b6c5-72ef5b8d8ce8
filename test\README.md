# OutlookManager 测试套件

这个测试套件用于验证 OutlookManager 项目的各项功能是否正常工作。

## 测试文件说明

### 1. `test_auth.py` - 认证功能测试
测试管理员密码验证和持久化功能：
- ✅ 正确密码验证
- ✅ 错误密码拒绝
- ✅ 未认证访问拒绝
- ✅ 认证后API访问
- ✅ 配置API访问

### 2. `test_outlook_import.py` - Outlook.txt导入功能测试
测试从outlook.txt格式导入账户的功能：
- ✅ 简单格式导入 (`邮箱---token`)
- ✅ 批量格式导入 (`邮箱----密码----client_id----token`)
- ✅ 混合格式导入
- ✅ 无效格式处理
- ✅ 空内容处理

### 3. `test_frontend.py` - 前端功能测试
使用Selenium测试前端界面功能：
- ✅ 管理员登录持久化
- ✅ Outlook.txt导入界面
- ✅ 账户列表显示

### 4. `run_all_tests.py` - 测试运行器
自动运行所有测试的主脚本：
- 🚀 自动启动服务器
- 🔍 检查测试依赖
- 📝 创建测试数据
- 🧪 运行所有测试
- 📊 生成测试报告
- 🧹 清理测试环境

## 运行测试

### 方法1: 运行所有测试（推荐）
```bash
cd test
python run_all_tests.py
```

### 方法2: 运行单个测试
```bash
# 确保服务器正在运行
python main.py

# 在另一个终端运行测试
cd test
python test_auth.py
python test_outlook_import.py
python test_frontend.py  # 需要Chrome浏览器和ChromeDriver
```

## 测试依赖

### Python包依赖
```bash
pip install requests selenium
```

### 前端测试额外依赖
- Chrome浏览器
- ChromeDriver (需要与Chrome版本匹配)

### 安装ChromeDriver
1. 查看Chrome版本: `chrome://version/`
2. 下载对应版本的ChromeDriver: https://chromedriver.chromium.org/
3. 将ChromeDriver添加到PATH环境变量

## 测试数据

测试使用虚假的邮箱账户和token，不会影响真实的Outlook账户。测试数据格式：

### 简单格式
```
<EMAIL>---fake_refresh_token_1
<EMAIL>---fake_refresh_token_2
```

### 批量格式
```
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_3
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_4
```

## 预期结果

由于使用的是虚假token，所有账户验证都会失败，但这是正常的。测试主要验证：
1. API端点是否正常响应
2. 数据格式是否正确解析
3. 错误处理是否正确
4. 前端界面是否正常显示

## 故障排除

### 服务器启动失败
- 检查端口8000是否被占用
- 确保项目依赖已安装: `pip install -r requirements.txt`

### 前端测试失败
- 确保Chrome浏览器已安装
- 确保ChromeDriver版本与Chrome匹配
- 检查ChromeDriver是否在PATH中

### 导入测试失败
- 检查API端点是否正确
- 检查认证是否正常工作
- 查看服务器日志了解详细错误

## 测试覆盖范围

✅ **已覆盖的功能**
- 管理员认证和持久化
- Outlook.txt文件解析和导入
- 前端界面基本功能
- API端点响应
- 错误处理

⚠️ **未覆盖的功能**
- 真实Outlook账户验证（需要真实token）
- 邮件读取功能（需要有效账户）
- 复杂的前端交互（需要更多Selenium测试）
- 性能测试
- 并发测试

## 贡献测试

如果你想添加更多测试：
1. 在相应的测试文件中添加测试函数
2. 确保测试函数名以`test_`开头
3. 使用清晰的打印输出说明测试步骤
4. 返回True表示成功，False表示失败
5. 更新这个README文档
