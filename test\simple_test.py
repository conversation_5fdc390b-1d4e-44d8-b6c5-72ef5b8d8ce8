#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试脚本，避免编码问题
"""

import requests
import json
import time
import sys
import os

# 测试配置
API_BASE = "http://localhost:8000"
ADMIN_PASSWORD = "admin123"

def test_auth():
    """测试认证功能"""
    print("测试认证功能...")
    
    try:
        # 测试正确密码
        response = requests.post(
            f"{API_BASE}/auth/verify",
            headers={"Authorization": f"Bearer {ADMIN_PASSWORD}"}
        )
        if response.status_code == 200:
            print("[OK] 正确密码验证成功")
        else:
            print(f"[FAIL] 正确密码验证失败: {response.status_code}")
            return False
        
        # 测试错误密码
        response = requests.post(
            f"{API_BASE}/auth/verify",
            headers={"Authorization": "Bearer wrong_password"}
        )
        if response.status_code == 401:
            print("[OK] 错误密码正确返回401")
        else:
            print(f"[FAIL] 错误密码应该返回401: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 认证测试失败: {e}")
        return False

def test_outlook_import():
    """测试Outlook导入功能"""
    print("测试Outlook导入功能...")
    
    # 测试数据
    test_content = """<EMAIL>---fake_token_1
<EMAIL>---fake_token_2
<EMAIL>----fake_pass----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_token_3"""
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": test_content}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"[OK] 导入请求成功，处理了 {len(results)} 个账户")
            
            # 检查结果格式
            for result in results:
                if 'email' in result and 'status' in result:
                    status = "[OK]" if result['status'] == 'success' else "[EXPECTED_FAIL]"
                    print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
                else:
                    print(f"[FAIL] 结果格式错误: {result}")
                    return False
            
            return True
        else:
            print(f"[FAIL] 导入请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 导入测试失败: {e}")
        return False

def test_accounts_api():
    """测试账户API"""
    print("测试账户API...")
    
    try:
        response = requests.get(
            f"{API_BASE}/accounts",
            headers={"Authorization": f"Bearer {ADMIN_PASSWORD}"}
        )
        
        if response.status_code == 200:
            accounts = response.json()
            print(f"[OK] 获取账户列表成功，共 {len(accounts)} 个账户")
            return True
        else:
            print(f"[FAIL] 获取账户列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[FAIL] 账户API测试失败: {e}")
        return False

def wait_for_server():
    """等待服务器启动"""
    print("等待服务器启动...")
    
    for i in range(30):
        try:
            response = requests.get(f"{API_BASE}/docs", timeout=2)
            if response.status_code == 200:
                print("[OK] 服务器已启动")
                return True
        except:
            pass
        time.sleep(1)
        if i % 5 == 0:
            print(f"等待中... ({i+1}/30)")
    
    print("[FAIL] 服务器启动超时")
    return False

def main():
    """主函数"""
    print("OutlookManager 简化测试")
    print(f"API地址: {API_BASE}")
    
    # 等待服务器
    if not wait_for_server():
        return False
    
    # 运行测试
    tests = [
        ("认证功能", test_auth),
        ("Outlook导入", test_outlook_import),
        ("账户API", test_accounts_api),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print(f"{'='*50}")
        
        if test_func():
            print(f"[OK] {test_name} 测试通过")
            passed += 1
        else:
            print(f"[FAIL] {test_name} 测试失败")
            failed += 1
    
    # 输出结果
    print(f"\n{'='*50}")
    print("测试结果总结")
    print(f"{'='*50}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    
    if failed == 0:
        print("\n[OK] 所有测试通过！")
        return True
    else:
        print(f"\n[FAIL] {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
