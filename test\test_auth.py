#!/usr/bin/env python3
"""
认证功能测试脚本
测试管理员密码验证和持久化功能
"""

import requests
import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 测试配置
API_BASE = "http://localhost:8000"
ADMIN_PASSWORD = "admin123"

def test_admin_auth():
    """测试管理员认证功能"""
    print("测试管理员认证功能...")

    # 测试1: 验证正确密码
    print("\n1. 测试正确密码验证...")
    try:
        response = requests.post(
            f"{API_BASE}/auth/verify",
            headers={"Authorization": f"Bearer {ADMIN_PASSWORD}"}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"[OK] 正确密码验证成功: {data['message']}")
        else:
            print(f"[FAIL] 正确密码验证失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"[FAIL] 请求失败: {e}")
        return False
    
    # 测试2: 验证错误密码
    print("\n2. 测试错误密码验证...")
    try:
        response = requests.post(
            f"{API_BASE}/auth/verify",
            headers={"Authorization": "Bearer wrong_password"}
        )
        if response.status_code == 401:
            print("✅ 错误密码正确返回401")
        else:
            print(f"❌ 错误密码应该返回401，实际返回: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 测试3: 测试其他API需要认证
    print("\n3. 测试其他API需要认证...")
    try:
        response = requests.get(f"{API_BASE}/accounts")
        if response.status_code == 401:
            print("✅ 未认证访问账户API正确返回401")
        else:
            print(f"❌ 未认证访问应该返回401，实际返回: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 测试4: 测试认证后访问API
    print("\n4. 测试认证后访问API...")
    try:
        response = requests.get(
            f"{API_BASE}/accounts",
            headers={"Authorization": f"Bearer {ADMIN_PASSWORD}"}
        )
        if response.status_code == 200:
            print("✅ 认证后访问账户API成功")
        else:
            print(f"❌ 认证后访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    print("\n🎉 管理员认证功能测试通过！")
    return True

def test_config_api():
    """测试配置API"""
    print("\n🔧 测试配置API...")
    
    try:
        response = requests.get(
            f"{API_BASE}/auth/config",
            headers={"Authorization": f"Bearer {ADMIN_PASSWORD}"}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 配置API访问成功: {data}")
        else:
            print(f"❌ 配置API访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    print("🎉 配置API测试通过！")
    return True

def main():
    """主测试函数"""
    print("🚀 开始认证功能测试...")
    print(f"API地址: {API_BASE}")
    print(f"管理员密码: {ADMIN_PASSWORD}")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    for i in range(10):
        try:
            response = requests.get(f"{API_BASE}/docs", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                break
        except:
            pass
        time.sleep(1)
        print(f"等待中... ({i+1}/10)")
    else:
        print("❌ 服务器启动超时，请确保服务器正在运行")
        return False
    
    # 运行测试
    success = True
    success &= test_admin_auth()
    success &= test_config_api()
    
    if success:
        print("\n🎉 所有认证测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
