#!/usr/bin/env python3
"""
运行所有测试的主脚本
"""

import subprocess
import sys
import os
import time
import requests
import threading
from pathlib import Path

# 测试配置
API_BASE = "http://localhost:8000"
TEST_DIR = Path(__file__).parent
PROJECT_ROOT = TEST_DIR.parent

def start_server():
    """启动服务器"""
    print("启动服务器...")
    try:
        # 切换到项目根目录
        os.chdir(PROJECT_ROOT)

        # 启动服务器
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):
            try:
                response = requests.get(f"{API_BASE}/docs", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功")
                    return process
            except:
                pass
            time.sleep(1)
            if i % 5 == 0:
                print(f"等待中... ({i+1}/30)")
        
        print("❌ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def run_test(test_file):
    """运行单个测试文件"""
    test_path = TEST_DIR / test_file
    if not test_path.exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    print(f"\n{'='*60}")
    print(f"🧪 运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            [sys.executable, str(test_path)],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        # 输出测试结果
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {test_file} 测试通过")
            return True
        else:
            print(f"❌ {test_file} 测试失败 (退出码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {test_file} 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行 {test_file} 时出错: {e}")
        return False

def check_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")
    
    required_packages = ['requests', 'selenium']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def create_test_data():
    """创建测试数据文件"""
    print("📝 创建测试数据...")
    
    # 创建测试用的outlook.txt文件
    test_outlook_txt = TEST_DIR / "test_outlook.txt"
    with open(test_outlook_txt, 'w', encoding='utf-8') as f:
        f.write("""<EMAIL>---fake_refresh_token_1
<EMAIL>---fake_refresh_token_2
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_3
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_4""")
    
    print(f"✅ 创建测试数据文件: {test_outlook_txt}")
    return True

def cleanup():
    """清理测试环境"""
    print("\n🧹 清理测试环境...")
    
    # 删除测试数据文件
    test_files = [
        TEST_DIR / "test_outlook.txt",
        PROJECT_ROOT / "accounts.json.backup",
    ]
    
    for file_path in test_files:
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"✅ 删除: {file_path}")
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")

def main():
    """主函数"""
    print("🎯 OutlookManager 完整测试套件")
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"测试目录: {TEST_DIR}")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺失的包后重试")
        return False
    
    # 创建测试数据
    if not create_test_data():
        print("\n❌ 创建测试数据失败")
        return False
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        print("\n❌ 服务器启动失败")
        return False
    
    try:
        # 运行测试
        test_files = [
            "test_auth.py",
            "test_outlook_import.py",
            # "test_frontend.py",  # 需要Chrome驱动，可能不是所有环境都有
        ]
        
        results = {}
        for test_file in test_files:
            results[test_file] = run_test(test_file)
        
        # 输出总结
        print(f"\n{'='*60}")
        print("📊 测试结果总结")
        print(f"{'='*60}")
        
        passed = 0
        failed = 0
        
        for test_file, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{test_file:<25} {status}")
            if success:
                passed += 1
            else:
                failed += 1
        
        print(f"\n总计: {passed} 个通过, {failed} 个失败")
        
        if failed == 0:
            print("\n🎉 所有测试通过！项目功能正常")
            return True
        else:
            print(f"\n❌ {failed} 个测试失败，请检查相关功能")
            return False
            
    finally:
        # 停止服务器
        if server_process:
            print("\n🛑 停止服务器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
                print("✅ 服务器已停止")
            except subprocess.TimeoutExpired:
                server_process.kill()
                print("⚠️ 强制停止服务器")
        
        # 清理环境
        cleanup()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
