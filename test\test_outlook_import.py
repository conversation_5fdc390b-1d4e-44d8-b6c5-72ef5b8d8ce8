#!/usr/bin/env python3
"""
Outlook.txt导入功能测试脚本
测试从outlook.txt格式导入账户的功能
"""

import requests
import json
import time
import sys
import os
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 测试配置
API_BASE = "http://localhost:8000"
ADMIN_PASSWORD = "admin123"

# 测试数据
SIMPLE_FORMAT_DATA = """<EMAIL>---fake_refresh_token_1
<EMAIL>---fake_refresh_token_2
<EMAIL>---fake_refresh_token_3"""

BATCH_FORMAT_DATA = """<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_4
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_5
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_6"""

MIXED_FORMAT_DATA = """<EMAIL>---fake_refresh_token_7
<EMAIL>----fake_password----9e5f94bc-e8a4-4e73-b8be-63364c29d753----fake_refresh_token_8
<EMAIL>---fake_refresh_token_9"""

INVALID_FORMAT_DATA = """invalid_email_format
<EMAIL>
<EMAIL>--incomplete_format
<EMAIL>----incomplete----format"""

def test_simple_format_import():
    """测试简单格式导入"""
    print("\n📥 测试简单格式导入 (邮箱---token)...")
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": SIMPLE_FORMAT_DATA}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 简单格式导入请求成功，处理了 {len(results)} 个账户")
            
            # 检查结果格式
            for result in results:
                if 'email' in result and 'status' in result:
                    status = "✅" if result['status'] == 'success' else "❌"
                    print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
                else:
                    print(f"❌ 结果格式错误: {result}")
                    return False
            
            return True
        else:
            print(f"❌ 简单格式导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_batch_format_import():
    """测试批量格式导入"""
    print("\n📥 测试批量格式导入 (邮箱----密码----client_id----token)...")
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": BATCH_FORMAT_DATA}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 批量格式导入请求成功，处理了 {len(results)} 个账户")
            
            # 检查结果格式
            for result in results:
                if 'email' in result and 'status' in result:
                    status = "✅" if result['status'] == 'success' else "❌"
                    print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
                else:
                    print(f"❌ 结果格式错误: {result}")
                    return False
            
            return True
        else:
            print(f"❌ 批量格式导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_mixed_format_import():
    """测试混合格式导入"""
    print("\n📥 测试混合格式导入...")
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": MIXED_FORMAT_DATA}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 混合格式导入请求成功，处理了 {len(results)} 个账户")
            
            # 检查结果格式
            for result in results:
                if 'email' in result and 'status' in result:
                    status = "✅" if result['status'] == 'success' else "❌"
                    print(f"  {status} {result['email']}: {result.get('message', 'N/A')}")
                else:
                    print(f"❌ 结果格式错误: {result}")
                    return False
            
            return True
        else:
            print(f"❌ 混合格式导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_invalid_format_import():
    """测试无效格式导入"""
    print("\n📥 测试无效格式导入...")
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": INVALID_FORMAT_DATA}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 无效格式导入请求成功，处理了 {len(results)} 个账户")
            
            # 应该没有成功的账户
            success_count = sum(1 for r in results if r.get('status') == 'success')
            if success_count == 0:
                print("✅ 正确处理了无效格式，没有成功导入任何账户")
                return True
            else:
                print(f"❌ 无效格式不应该成功导入账户，但成功了 {success_count} 个")
                return False
        else:
            print(f"❌ 无效格式导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_empty_content():
    """测试空内容导入"""
    print("\n📥 测试空内容导入...")
    
    try:
        response = requests.post(
            f"{API_BASE}/accounts/import-outlook-txt",
            headers={
                "Authorization": f"Bearer {ADMIN_PASSWORD}",
                "Content-Type": "application/json"
            },
            json={"content": ""}
        )
        
        if response.status_code == 200:
            results = response.json()
            if len(results) == 0:
                print("✅ 空内容正确返回空结果")
                return True
            else:
                print(f"❌ 空内容应该返回空结果，实际返回 {len(results)} 个结果")
                return False
        else:
            print(f"❌ 空内容导入失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Outlook.txt导入功能测试...")
    print(f"API地址: {API_BASE}")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    for i in range(10):
        try:
            response = requests.get(f"{API_BASE}/docs", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                break
        except:
            pass
        time.sleep(1)
        print(f"等待中... ({i+1}/10)")
    else:
        print("❌ 服务器启动超时，请确保服务器正在运行")
        return False
    
    # 运行测试
    success = True
    success &= test_simple_format_import()
    success &= test_batch_format_import()
    success &= test_mixed_format_import()
    success &= test_invalid_format_import()
    success &= test_empty_content()
    
    if success:
        print("\n🎉 所有Outlook.txt导入测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
