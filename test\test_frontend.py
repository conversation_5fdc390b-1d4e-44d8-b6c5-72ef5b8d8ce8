#!/usr/bin/env python3
"""
前端功能测试脚本
使用Selenium测试前端界面功能
"""

import time
import sys
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 测试配置
FRONTEND_URL = "http://localhost:8000"
ADMIN_PASSWORD = "admin123"

def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Chrome驱动设置失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return None

def test_admin_login_persistence(driver):
    """测试管理员登录持久化"""
    print("\n🔐 测试管理员登录持久化...")
    
    try:
        # 访问首页
        driver.get(FRONTEND_URL)
        wait = WebDriverWait(driver, 10)
        
        # 应该显示登录界面
        login_card = wait.until(EC.presence_of_element_located((By.ID, "adminLoginCard")))
        if not login_card.is_displayed():
            print("❌ 首次访问应该显示登录界面")
            return False
        
        # 输入密码
        password_input = driver.find_element(By.ID, "adminPasswordInput")
        password_input.send_keys(ADMIN_PASSWORD)
        
        # 点击登录
        login_button = driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
        login_button.click()
        
        # 等待进入邮件管理界面
        wait.until(EC.presence_of_element_located((By.ID, "loginCard")))
        
        # 检查是否成功进入管理界面
        if driver.find_element(By.ID, "adminLoginCard").is_displayed():
            print("❌ 登录后应该隐藏登录界面")
            return False
        
        print("✅ 管理员登录成功")
        
        # 刷新页面，测试持久化
        driver.refresh()
        time.sleep(2)
        
        # 检查是否仍然在管理界面（不需要重新登录）
        try:
            # 如果显示登录界面，说明持久化失败
            admin_login = driver.find_element(By.ID, "adminLoginCard")
            if admin_login.is_displayed():
                print("❌ 页面刷新后需要重新登录，持久化失败")
                return False
            else:
                print("✅ 页面刷新后仍保持登录状态，持久化成功")
                return True
        except NoSuchElementException:
            print("✅ 页面刷新后仍保持登录状态，持久化成功")
            return True
            
    except TimeoutException:
        print("❌ 页面加载超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_outlook_txt_import_ui(driver):
    """测试Outlook.txt导入界面"""
    print("\n📥 测试Outlook.txt导入界面...")
    
    try:
        wait = WebDriverWait(driver, 10)
        
        # 确保在管理界面
        if driver.find_element(By.ID, "adminLoginCard").is_displayed():
            print("❌ 需要先登录")
            return False
        
        # 切换到批量登录标签
        batch_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '批量登录')]")))
        batch_tab.click()
        
        # 检查是否有导入按钮
        import_button = wait.until(EC.presence_of_element_located((By.XPATH, "//button[contains(text(), '导入 outlook.txt')]")))
        if not import_button.is_displayed():
            print("❌ 导入按钮不可见")
            return False
        
        print("✅ 导入按钮存在且可见")
        
        # 检查是否有清空按钮
        clear_button = driver.find_element(By.XPATH, "//button[contains(text(), '清空')]")
        if not clear_button.is_displayed():
            print("❌ 清空按钮不可见")
            return False
        
        print("✅ 清空按钮存在且可见")
        
        # 检查文本区域
        textarea = driver.find_element(By.ID, "batchAccounts")
        if not textarea.is_displayed():
            print("❌ 批量输入文本区域不可见")
            return False
        
        print("✅ 批量输入文本区域存在且可见")
        
        # 测试清空功能
        textarea.send_keys("test content")
        clear_button.click()
        
        if textarea.get_attribute("value") != "":
            print("❌ 清空功能不工作")
            return False
        
        print("✅ 清空功能正常工作")
        
        return True
        
    except TimeoutException:
        print("❌ 界面元素加载超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_account_list_display(driver):
    """测试账户列表显示"""
    print("\n📋 测试账户列表显示...")
    
    try:
        wait = WebDriverWait(driver, 10)
        
        # 切换到账户管理标签
        manage_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '账户管理')]")))
        manage_tab.click()
        
        # 等待账户列表加载
        account_list = wait.until(EC.presence_of_element_located((By.ID, "accountList")))
        
        # 检查账户数量显示
        account_count = driver.find_element(By.ID, "accountCount")
        if not account_count.is_displayed():
            print("❌ 账户数量显示不可见")
            return False
        
        print(f"✅ 账户数量显示: {account_count.text}")
        
        # 检查搜索框
        search_input = driver.find_element(By.ID, "accountSearch")
        if not search_input.is_displayed():
            print("❌ 搜索框不可见")
            return False
        
        print("✅ 搜索框存在且可见")
        
        # 检查全选框
        select_all = driver.find_element(By.ID, "selectAllAccounts")
        if not select_all.is_displayed():
            print("❌ 全选框不可见")
            return False
        
        print("✅ 全选框存在且可见")
        
        return True
        
    except TimeoutException:
        print("❌ 界面元素加载超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始前端功能测试...")
    print(f"前端地址: {FRONTEND_URL}")
    
    # 设置驱动
    driver = setup_driver()
    if not driver:
        return False
    
    try:
        # 等待服务器启动
        print("\n⏳ 等待服务器启动...")
        for i in range(10):
            try:
                driver.get(FRONTEND_URL)
                if "OutlookManager" in driver.title or driver.find_elements(By.TAG_NAME, "body"):
                    print("✅ 前端服务器已启动")
                    break
            except:
                pass
            time.sleep(1)
            print(f"等待中... ({i+1}/10)")
        else:
            print("❌ 前端服务器启动超时")
            return False
        
        # 运行测试
        success = True
        success &= test_admin_login_persistence(driver)
        success &= test_outlook_txt_import_ui(driver)
        success &= test_account_list_display(driver)
        
        if success:
            print("\n🎉 所有前端测试通过！")
            return True
        else:
            print("\n❌ 部分前端测试失败")
            return False
            
    finally:
        driver.quit()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {e}")
        sys.exit(1)
